{"name": "Cosmos RAG Agent", "nodes": [{"parameters": {"sessionIdType": "customKey", "sessionKey": "vector_store_rag"}, "type": "@n8n/n8n-nodes-langchain.memoryZep", "typeVersion": 1.3, "position": [980, 420], "id": "514a2e13-7f72-4ed3-81f0-abfc1c1628b7", "name": "Zep", "credentials": {"zepApi": {"id": "0NHDyU3wNsPkk7JN", "name": "Zep Api account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $('Set URL/Name').item.json.file_name }}", "rightValue": ".pdf", "operator": {"type": "string", "operation": "contains"}, "id": "b25c7395-7826-419e-aba6-2dda9c12f914"}], "combinator": "and"}, "renameOutput": true, "outputKey": "PDF"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "1d3c9db9-097f-4526-8300-a874fce8b04f", "leftValue": "={{ $('Set URL/Name').item.json.file_name.endsWith('.txt') || $('Set URL/Name').item.json.file_name.endsWith('.docx') }}", "rightValue": "={{ $json.file_name.endsWith('.txt') || $json.file_name.endsWith('.docx') }}", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Plain Text"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "7d6d63dc-77b1-41e5-865b-4b0e2ee66386", "leftValue": "={{ $('Set URL/Name').item.json.file_name }}", "rightValue": ".xlsx", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "XLSX"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "d2857152-dad2-4286-b5a2-4c025ded7183", "leftValue": "={{ $('Set URL/Name').item.json.file_name }}", "rightValue": ".xls", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "XLS"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a7b922ad-15e3-435b-93c4-36cb763d0227", "leftValue": "={{ $('Set URL/Name').item.json.file_name }}", "rightValue": ".csv", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "CSV"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "64b5e8a7-e6d7-459f-91e6-b0c07689ee22", "leftValue": "={{ $('Set URL/Name').item.json.file_name }}", "rightValue": ".json", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "JSON"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "435e2bde-e4d8-4079-8952-652ccef0c813", "leftValue": "={{ $('Set URL/Name').item.json.file_name }}", "rightValue": ".html", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "HTML"}]}, "options": {"fallbackOutput": "extra"}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-100, 1060], "id": "bd448c98-cda9-4b38-95b2-07195f4f16e4", "name": "Document Router"}, {"parameters": {"operation": "pdf", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [600, 800], "id": "d2415408-0c93-4174-ab3b-c9ec70315ca2", "name": "Extract from PDF"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [600, 1520], "id": "4cb8272a-39fc-473a-89ff-93d136ed3dda", "name": "Extract from CSV"}, {"parameters": {"operation": "xlsx", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [600, 1160], "id": "e1ad682d-550d-46f4-9d23-e1952de3a40a", "name": "Extract from XLSX"}, {"parameters": {"operation": "text", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [600, 980], "id": "783fe9ab-aa1f-4dbc-b790-4960b7819431", "name": "Extract from Text"}, {"parameters": {"operation": "fromJson", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [600, 1700], "id": "fbf6db68-db6f-4375-b89a-469bd48a4362", "name": "Extract from JSON"}, {"parameters": {"operation": "html", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [600, 1880], "id": "d9131e3f-4671-496a-aa4a-3271d4d2f78f", "name": "Extract from HTML"}, {"parameters": {"inputText": "={{ $json.text }}", "categories": {"categories": [{"category": "AI News", "description": "Determine whether this is related exclusively to AI News"}, {"category": "AI Prompts", "description": "Determine whether this is related exclusively to AI Prompting"}, {"category": "AI Tools", "description": "Determine whether this is related exclusively to AI Tools"}, {"category": "Personal Business", "description": "Determine whether this is for personal business (eg. related to marketing, advertising, business strategy, business documentation)"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.textClassifier", "typeVersion": 1, "position": [1440, 1340], "id": "19e29684-660a-4c0b-84d7-9ce7adeb7a9e", "name": "Text Classifier"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "gpt-4o"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1440, 1520], "id": "4f0ae57c-4bd7-4caf-923b-32238815d282", "name": "4o", "credentials": {"openAiApi": {"id": "8YgeNpqwMCNZGTAG", "name": "OpenAi account"}}}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "gpt-4o"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [840, 420], "id": "5e267f47-2e01-4ff9-b3bf-d91a6e48b5db", "name": "4ox", "credentials": {"openAiApi": {"id": "8YgeNpqwMCNZGTAG", "name": "OpenAi account"}}}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "id": "2ea0a07d-3a97-4cdf-83a5-a01569918a39", "name": "Aggregate", "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [840, 1340]}, {"parameters": {"fieldsToSummarize": {"values": [{"aggregation": "concatenate", "field": "data"}]}, "options": {}}, "id": "af2fbe81-c1b6-4865-b0d0-0859c5dc8a24", "name": "Summarize", "type": "n8n-nodes-base.summarize", "typeVersion": 1, "position": [1000, 1340]}, {"parameters": {"httpMethod": "POST", "path": "chat", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [720, 40], "id": "5f333198-91f6-408c-ad16-e1e4f18a68db", "name": "Webhook1", "webhookId": "fe98149e-bbae-4d4c-bd4d-bea00631adec"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [1820, 40], "id": "54284f68-a31f-4808-96b2-cf7cea55f2d8", "name": "Respond to Webhook"}, {"parameters": {"url": "={{ $json.URL }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "supabaseApi", "options": {"response": {"response": {"responseFormat": "file"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-380, 1140], "id": "b9598bdc-11c5-4486-bfff-1303eb80259e", "name": "Download File", "credentials": {"supabaseApi": {"id": "c6rfnV1icbriSz77", "name": "Supabase account"}}}, {"parameters": {"url": "https://youtube-transcript3.p.rapidapi.com/api/transcript-with-url", "sendQuery": true, "queryParameters": {"parameters": [{"name": "url", "value": "={{ $json.URL }}"}, {"name": "flat_text", "value": "true"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-rapidapi-key", "value": "**************************************************"}, {"name": "x-rapidapi-host", "value": "youtube-transcript3.p.rapidapi.com"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [600, 600], "id": "aacbd6e7-ce7b-41ef-99f7-434daf7d5454", "name": "Transcribe"}, {"parameters": {"assignments": {"assignments": [{"id": "bce8ef3d-5884-4990-b372-f860e1c6a225", "name": "file_name", "value": "={{ $json.body.originalFilename }}", "type": "string"}, {"id": "679b9d7a-ec1d-40d8-abb2-f699f37ef2d0", "name": "URL", "value": "={{ $json.body.downloadUrl }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-640, 1140], "id": "6a2f7653-30bf-4d02-9298-6eb38d74d9db", "name": "Set URL/Name"}, {"parameters": {"assignments": {"assignments": [{"id": "679b9d7a-ec1d-40d8-abb2-f699f37ef2d0", "name": "URL", "value": "={{ $json.body.youtubeUrl }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [320, 600], "id": "9e270905-973d-4c5d-856f-9883da62c3ad", "name": "YouTube URL"}, {"parameters": {"method": "POST", "url": "https://api.tavily.com/search", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer 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"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"query\": \"{{ $json.query }}\",\n  \"topic\": \"general\",\n  \"search_depth\": \"advanced\",\n  \"chunks_per_source\": 3,\n  \"max_results\": 5,\n  \"time_range\": \"day\",\n  \"days\": 1,\n  \"include_answer\": \"advanced\",\n  \"include_raw_content\": false,\n  \"include_images\": false,\n  \"include_image_descriptions\": false,\n  \"include_domains\": [],\n  \"exclude_domains\": []\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [600, 400], "id": "c4911718-5fef-4ff9-a87b-5cd26366b979", "name": "<PERSON><PERSON>"}, {"parameters": {"assignments": {"assignments": [{"id": "bce8ef3d-5884-4990-b372-f860e1c6a225", "name": "query", "value": "={{ $json.body.newsQuery }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [320, 400], "id": "00593e94-6f70-4fe8-afe2-6b38a0620453", "name": "Query"}, {"parameters": {"httpMethod": "POST", "path": "news", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [40, 400], "id": "0d968c68-f83e-4d66-8929-3e9b61d82b9d", "name": "News", "webhookId": "fe98149e-bbae-4d4c-bd4d-bea00631adec"}, {"parameters": {"httpMethod": "POST", "path": "youtube", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [40, 600], "id": "12aa6367-234b-46f3-8b3d-539e85440cd3", "name": "YouTube", "webhookId": "fe98149e-bbae-4d4c-bd4d-bea00631adec"}, {"parameters": {"httpMethod": "POST", "path": "fileupload", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-920, 1140], "id": "fef9ec47-49a4-47b9-80db-bb0744fc98d4", "name": "Files", "webhookId": "fe98149e-bbae-4d4c-bd4d-bea00631adec"}, {"parameters": {"assignments": {"assignments": [{"id": "9a9a245e-f1a1-4282-bb02-a81ffe629f0f", "name": "chatInput", "value": "={{ $json?.chatInput || $json.body.chatInput || $json.body.query }}", "type": "string"}, {"id": "c33b107c-0ffe-4757-9be5-869de6da202d", "name": "body.type", "value": "={{ $json.body.type }}", "type": "string"}]}, "options": {}}, "id": "ffe86918-9edf-4186-8784-229c7da251c8", "name": "<PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [940, 40]}, {"parameters": {"assignments": {"assignments": [{"id": "0c7a1192-b621-4a72-bb1d-2050cef7d1d4", "name": "text", "value": "={{ $json.answer || $json.transcript || $json.data || $json.text || $json.concatenated_data }}", "type": "string"}, {"id": "8a200d36-25f4-4b4b-b87b-ef80b9f4a5c9", "name": "date", "value": "={{ $now }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1240, 1340], "id": "967ced83-fa56-4a4d-8326-7153d5c98dfa", "name": "Set Text"}, {"parameters": {"operation": "get", "tableId": "system_prompts", "filters": {"conditions": [{"keyName": "type", "keyValue": "={{ $json.body.type }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1160, 40], "id": "7db04cb6-2641-4ac5-a6fc-826eefc7b401", "name": "Get Prompts", "credentials": {"supabaseApi": {"id": "c6rfnV1icbriSz77", "name": "Supabase account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [2420, 720], "id": "e638b63b-e470-42f4-9b2d-2155df7d7f3d", "name": "Embeddings OpenAI", "credentials": {"openAiApi": {"id": "8YgeNpqwMCNZGTAG", "name": "OpenAi account"}}}, {"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $json.answer || $json.transcript || $json.data || $json.text || $json.concatenated_data }}", "options": {"metadata": {"metadataValues": [{"name": "creation_date", "value": "={{ $('Text Classifier').item.json.date }}"}]}}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [2580, 720], "id": "72de74c4-1488-4003-994b-266ae0312a16", "name": "Default Data Loader"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.textSplitterCharacterTextSplitter", "typeVersion": 1, "position": [2600, 860], "id": "ce022172-e98c-4f2c-87e3-0bb8f9f73b01", "name": "Character Text Splitter"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [2420, 1280], "id": "24c9621d-a070-4628-b926-2825118f08b9", "name": "Embeddings OpenAI1", "credentials": {"openAiApi": {"id": "8YgeNpqwMCNZGTAG", "name": "OpenAi account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [2420, 1840], "id": "21f1191a-aa14-463a-b7e9-507e5b6f30ed", "name": "Embeddings OpenAI2", "credentials": {"openAiApi": {"id": "8YgeNpqwMCNZGTAG", "name": "OpenAi account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [2420, 2400], "id": "4895f556-8887-4dbb-b110-fd96caf930bf", "name": "Embeddings OpenAI3", "credentials": {"openAiApi": {"id": "8YgeNpqwMCNZGTAG", "name": "OpenAi account"}}}, {"parameters": {"mode": "insert", "pineconeIndex": {"__rl": true, "value": "cosmos", "mode": "list", "cachedResultName": "cosmos"}, "options": {"pineconeNamespace": "ai_prompts"}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1, "position": [2440, 1060], "id": "641f8afb-5e5a-4535-a97c-e8e555851934", "name": "Upload to AI Prompts", "credentials": {"pineconeApi": {"id": "CfhcNz156aFhl4QP", "name": "PineconeApi account"}}}, {"parameters": {"mode": "insert", "pineconeIndex": {"__rl": true, "value": "cosmos", "mode": "list", "cachedResultName": "cosmos"}, "options": {"pineconeNamespace": "ai_tools"}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1, "position": [2440, 1620], "id": "c9955e10-752e-44d8-898c-af7c46b75ccc", "name": "Upload to AI Tools", "credentials": {"pineconeApi": {"id": "CfhcNz156aFhl4QP", "name": "PineconeApi account"}}}, {"parameters": {"mode": "insert", "pineconeIndex": {"__rl": true, "value": "cosmos", "mode": "list", "cachedResultName": "cosmos"}, "options": {"pineconeNamespace": "personal_business"}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1, "position": [2440, 2180], "id": "3542faaf-bc74-4b8a-9fb8-fb31fa82e229", "name": "Upload to Business", "credentials": {"pineconeApi": {"id": "CfhcNz156aFhl4QP", "name": "PineconeApi account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [1160, 600], "id": "a3cd7a7c-6214-445d-91d9-c591dd166acb", "name": "Embeddings OpenAI4", "credentials": {"openAiApi": {"id": "8YgeNpqwMCNZGTAG", "name": "OpenAi account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [1480, 600], "id": "970f8369-fe02-431b-9d89-0fd671469482", "name": "Embeddings OpenAI5", "credentials": {"openAiApi": {"id": "8YgeNpqwMCNZGTAG", "name": "OpenAi account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [1800, 600], "id": "185dec5c-1972-44ab-8cd2-f43ec8e4597b", "name": "Embeddings OpenAI6", "credentials": {"openAiApi": {"id": "8YgeNpqwMCNZGTAG", "name": "OpenAi account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [2120, 600], "id": "b19b393c-5aa5-48cd-b37c-da3b8c1ac078", "name": "Embeddings OpenAI7", "credentials": {"openAiApi": {"id": "8YgeNpqwMCNZGTAG", "name": "OpenAi account"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "ai_news", "toolDescription": "Use RAG to look up information in the for AI news", "pineconeIndex": {"__rl": true, "value": "cosmos", "mode": "list", "cachedResultName": "cosmos"}, "options": {"pineconeNamespace": "ai_news"}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1, "position": [1120, 420], "id": "5cfecf25-cb8c-4ce4-94f3-e109c8b7b245", "name": "RAG AI News", "credentials": {"pineconeApi": {"id": "CfhcNz156aFhl4QP", "name": "PineconeApi account"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "ai_prompts", "toolDescription": "Use RAG to look up information for AI Prompts", "pineconeIndex": {"__rl": true, "value": "cosmos", "mode": "list", "cachedResultName": "cosmos"}, "options": {"pineconeNamespace": "ai_prompts"}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1, "position": [1440, 420], "id": "adba0803-f0eb-40c8-9b0c-5a0b064ad054", "name": "RAG AI Prompts", "credentials": {"pineconeApi": {"id": "CfhcNz156aFhl4QP", "name": "PineconeApi account"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "ai_tools", "toolDescription": "Use RAG to look up information for AI Tools", "pineconeIndex": {"__rl": true, "value": "cosmos", "mode": "list", "cachedResultName": "cosmos"}, "options": {"pineconeNamespace": "ai_tools"}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1, "position": [1760, 420], "id": "9eb0f535-7e51-472e-ba5f-7582e6b54d1b", "name": "RAG AI Tools", "credentials": {"pineconeApi": {"id": "CfhcNz156aFhl4QP", "name": "PineconeApi account"}}}, {"parameters": {"mode": "retrieve-as-tool", "toolName": "personal_business", "toolDescription": "Use RAG to look up information for Personal Business", "pineconeIndex": {"__rl": true, "value": "cosmos", "mode": "list", "cachedResultName": "cosmos"}, "options": {"pineconeNamespace": "personal_business"}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1, "position": [2080, 420], "id": "98db42c9-0f18-40b1-9886-bcc10e03b03c", "name": "RAG Personal Business", "credentials": {"pineconeApi": {"id": "CfhcNz156aFhl4QP", "name": "PineconeApi account"}}}, {"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $json.answer || $json.transcript || $json.data || $json.text || $json.concatenated_data }}", "options": {"metadata": {"metadataValues": [{"name": "creation_date", "value": "={{ $('Text Classifier').item.json.date }}"}]}}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [2580, 1280], "id": "35da793f-d1de-4fcd-a115-76071f228357", "name": "Default Data Loader1"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.textSplitterCharacterTextSplitter", "typeVersion": 1, "position": [2600, 1420], "id": "1f3c2ade-0ec8-4a02-b86b-d2fdb5d7d30a", "name": "Character Text Splitter1"}, {"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $json.answer || $json.transcript || $json.data || $json.text || $json.concatenated_data }}", "options": {"metadata": {"metadataValues": [{"name": "creation_date", "value": "={{ $('Text Classifier').item.json.date }}"}]}}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [2580, 1840], "id": "b401dba6-622e-45f9-9114-cad2790ccb38", "name": "Default Data Loader2"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.textSplitterCharacterTextSplitter", "typeVersion": 1, "position": [2600, 1980], "id": "1c96dbdc-6c08-4b65-9b1d-3e47ec8a521f", "name": "Character Text Splitter2"}, {"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $json.answer || $json.transcript || $json.data || $json.text || $json.concatenated_data }}", "options": {"metadata": {"metadataValues": [{"name": "creation_date", "value": "={{ $('Text Classifier').item.json.date }}"}]}}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [2580, 2400], "id": "b38d35de-21b6-4921-853f-33356d43f2ef", "name": "Default Data Loader3"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.textSplitterCharacterTextSplitter", "typeVersion": 1, "position": [2600, 2540], "id": "818aa2c7-30e5-42b4-a8d1-08b573871d05", "name": "Character Text Splitter3"}, {"parameters": {"content": "## Ensure your vector stores are set to use the correct index and namespaces.", "height": 120, "width": 380, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [2780, 1520], "typeVersion": 1, "id": "b371c14d-f50c-4167-973b-b69b05c29045", "name": "Sticky Note4"}, {"parameters": {"content": "## Ensure your Vector stores are each using their own namespace.", "height": 120, "width": 380, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [2580, 60], "typeVersion": 1, "id": "c1dae881-4e13-4233-8343-872cd0da6eff", "name": "Sticky Note5"}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $('Google Drive Trigger').item.json.id }}", "mode": "id"}, "options": {"googleFileConversion": {"conversion": {"docsToFormat": "text/plain"}}}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-380, 1540], "id": "25a22152-72cc-4f53-ba55-43dac3f9d590", "name": "Google Drive", "credentials": {"googleDriveOAuth2Api": {"id": "mEkbz8YufiigCqM6", "name": "Google Drive account"}}}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "1AmEVCK5bqt6p1vBa-eNBiGrVZ6boEB82", "mode": "list", "cachedResultName": "Vector", "cachedResultUrl": "https://drive.google.com/drive/folders/1AmEVCK5bqt6p1vBa-eNBiGrVZ6boEB82"}, "event": "fileCreated", "options": {}}, "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-920, 1440], "id": "000a833d-4963-4b5f-8a58-23ab1a462f41", "name": "Google Drive Trigger", "credentials": {"googleDriveOAuth2Api": {"id": "mEkbz8YufiigCqM6", "name": "Google Drive account"}}}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "1bHdtKfvUaBnVkJKxdkBHWt1tmxlvvm68", "mode": "list", "cachedResultName": "Meetings", "cachedResultUrl": "https://drive.google.com/drive/folders/1bHdtKfvUaBnVkJKxdkBHWt1tmxlvvm68"}, "event": "fileUpdated", "options": {}}, "id": "aa24000f-0223-448b-84ff-326e4123b083", "name": "File Updated", "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-920, 1640], "credentials": {"googleDriveOAuth2Api": {"id": "mEkbz8YufiigCqM6", "name": "Google Drive account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "bce8ef3d-5884-4990-b372-f860e1c6a225", "name": "file_name", "value": "={{ $json.name }}", "type": "string"}, {"id": "********-26ae-4899-b58b-a8b31180438d", "name": "mime_type", "value": "={{ $json.mimeType }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-640, 1540], "id": "77ab990b-cdea-40b6-91c3-69f8df30cf57", "name": "Set File Name/Mime"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.file_name }}", "rightValue": ".pdf", "operator": {"type": "string", "operation": "contains"}, "id": "b25c7395-7826-419e-aba6-2dda9c12f914"}], "combinator": "and"}, "renameOutput": true, "outputKey": "PDF"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "1d3c9db9-097f-4526-8300-a874fce8b04f", "leftValue": "={{ $('Set File Name/Mime').item.json.mime_type === 'application/vnd.google-apps.document' || $('Set URL/Name').item.json.file_name.endsWith('.txt') || $('Set URL/Name').item.json.file_name.endsWith('.docx') }}", "rightValue": "={{ $('Set File Name/Mime').item.json.mime_type === 'application/vnd.google-apps.document' || $('Set URL/Name').item.json.file_name.endsWith('.txt') || $('Set URL/Name').item.json.file_name.endsWith('.docx') }}", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Plain Text"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "7d6d63dc-77b1-41e5-865b-4b0e2ee66386", "leftValue": "={{ $json.file_name }}", "rightValue": ".xlsx", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "XLSX"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "04177624-d9f0-4038-8367-1877fc3bdf88", "leftValue": "={{ $json.file_name }}", "rightValue": ".xls", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "XLS"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a7b922ad-15e3-435b-93c4-36cb763d0227", "leftValue": "={{ $json.file_name }}", "rightValue": ".xlsx", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "CSV"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "64b5e8a7-e6d7-459f-91e6-b0c07689ee22", "leftValue": "={{ $json.file_name }}", "rightValue": ".csv", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "JSON"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "435e2bde-e4d8-4079-8952-652ccef0c813", "leftValue": "={{ $json.file_name }}", "rightValue": ".html", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "HTML"}]}, "options": {"fallbackOutput": "extra"}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-100, 1460], "id": "8cc8bdae-01e1-4cc3-b937-6e5a58d496d2", "name": "Document Router1"}, {"parameters": {"operation": "xls", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [600, 1340], "id": "ffc90507-27ff-4d92-afdc-94aade359406", "name": "Extract from XLS"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Drop the existing table if it exists (optional, for a clean start in testing)\n-- DROP TABLE IF EXISTS system_prompts;\n\n-- Create the table for system prompts with updated CHECK constraint\nCREATE TABLE system_prompts (\n    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),\n    type text NOT NULL CHECK (type IN ('Personal Business', 'AI Prompts', 'AI News', 'AI Tools')),\n    prompt text NOT NULL,\n    created_at timestamp with time zone DEFAULT now() NOT NULL\n);\n\n-- Add comments to the table and columns for clarity\nCOMMENT ON TABLE system_prompts IS 'Stores system prompts used to configure AI agent behavior based on context.';\nCOMMENT ON COLUMN system_prompts.type IS 'The context type (e.g., Personal Business, AI Prompts, AI News, AI Tools) determining the knowledge base and behavior.';\nCOMMENT ON COLUMN system_prompts.prompt IS 'The detailed system message/instructions for the AI agent.';\nCOMMENT ON COLUMN system_prompts.created_at IS 'Timestamp when the prompt was created.';\n\n-- Insert the system prompt for Personal Business (Unchanged)\nINSERT INTO system_prompts (type, prompt)\nVALUES (\n    'Personal Business',\n    'You are a helpful assistant specializing in analyzing and answering questions based on a private collection of personal business documents. Your knowledge base consists of text documents (like notes, extracted PDFs, DOCX files) and potentially tabular data (CSVs, Excel sheets) stored within the ''Personal Business'' vector store and associated data tables.\n\nYour primary goal is to provide accurate answers derived *only* from this specific knowledge base.\n\nInstructions:\n1.  **Prioritize RAG:** Always start by performing Retrieval-Augmented Generation (RAG) against the ''Personal Business'' vector store to find relevant information for the user''s query.\n2.  **Handle Tabular Data:** If the question explicitly requires calculations (sum, average, max, min), specific row lookups, or filtering on structured data that RAG might handle unreliably, use your tools to query the relevant tabular data sources associated with the ''Personal Business'' context. Confirm if such tools are available before attempting.\n3.  **Document Exploration (If RAG Fails):** If the initial RAG search yields no relevant results, use your tools to list available documents within the ''Personal Business'' knowledge base. Identify potentially relevant documents based on titles or metadata, extract their content, and then attempt to answer the question based on that extracted text.\n4.  **Be Honest:** If, after attempting RAG and targeted document analysis (and potential tabular queries), you cannot find a relevant answer within the provided ''Personal Business'' knowledge base, clearly state that the information is not available in your current documents. Do *not* invent answers or use external knowledge.'\n);\n\n-- Insert the system prompt for AI Prompts\nINSERT INTO system_prompts (type, prompt)\nVALUES (\n    'AI Prompts',\n    'You are a specialized AI assistant focused on AI Prompt Engineering. Your knowledge base consists *only* of information stored within the **''AI Prompts''** vector store, containing examples, best practices, and guides related to creating effective prompts for AI models.\n\nYour primary goal is to provide accurate answers and guidance derived *exclusively* from this specific knowledge base.\n\nInstructions:\n1.  **Prioritize RAG:** Always start by performing Retrieval-Augmented Generation (RAG) against the **''AI Prompts''** vector store to find relevant examples or guidance for the user''s query about prompts.\n2.  **Document Exploration (If RAG Fails):** If the initial RAG search yields no relevant results, use your tools (if available) to list document metadata within the **''AI Prompts''** store. Identify potentially relevant documents, extract their content, and then attempt to answer the question based on that extracted text.\n3.  **Focus on Provided Knowledge:** Your answers should strictly rely on the content within the **''AI Prompts''** store. Do *not* use external knowledge or information from other vector stores (like AI News or AI Tools).\n4.  **Be Honest:** If, after searching the designated **''AI Prompts''** vector store, you cannot find a relevant answer or example, clearly state that the information is not available within your current knowledge base regarding AI Prompts. Do *not* invent answers.'\n);\n\n-- Insert the system prompt for AI News\nINSERT INTO system_prompts (type, prompt)\nVALUES (\n    'AI News',\n    'You are an AI assistant dedicated to providing information about recent developments and news in the field of Artificial Intelligence. Your knowledge base is sourced *exclusively* from the **''AI News''** vector store, which contains summaries and articles about AI breakthroughs, research, events, and industry trends.\n\nYour primary goal is to provide accurate and up-to-date news-related answers based *only* on the information contained within this specific vector store.\n\nInstructions:\n1.  **Prioritize RAG:** Always start by performing Retrieval-Augmented Generation (RAG) against the **''AI News''** vector store to find relevant news items or developments related to the user''s query.\n2.  **Document Exploration (If RAG Fails):** If the initial RAG search yields no relevant results, use your tools (if available) to list document metadata within the **''AI News''** store. Identify potentially relevant articles, extract their content, and then attempt to answer the question based on that extracted text.\n3.  **Focus on Provided Knowledge:** Your answers should strictly rely on the content within the **''AI News''** store. Do *not* use external knowledge, real-time web search (unless equipped with a specific tool for it), or information from other vector stores (like AI Prompts or AI Tools).\n4.  **Be Honest:** If, after searching the designated **''AI News''** vector store, you cannot find relevant information on the topic, clearly state that the specific news or development is not available within your current AI News knowledge base. Do *not* invent answers.'\n);\n\n-- Insert the system prompt for AI Tools\nINSERT INTO system_prompts (type, prompt)\nVALUES (\n    'AI Tools',\n    'You are an AI assistant focused on providing information about various AI tools, platforms, libraries, and software. Your knowledge base consists *solely* of information stored within the **''AI Tools''** vector store, covering details about functionalities, use cases, developers, and comparisons of different AI tools.\n\nYour primary goal is to provide accurate descriptions and information about AI tools derived *only* from this specific knowledge base.\n\nInstructions:\n1.  **Prioritize RAG:** Always start by performing Retrieval-Augmented Generation (RAG) against the **''AI Tools''** vector store to find relevant information about the specific tool(s) or tool categories mentioned in the user''s query.\n2.  **Document Exploration (If RAG Fails):** If the initial RAG search yields no relevant results, use your tools (if available) to list document metadata within the **''AI Tools''** store. Identify potentially relevant tool descriptions or documents, extract their content, and then attempt to answer the question based on that extracted text.\n3.  **Focus on Provided Knowledge:** Your answers should strictly rely on the content within the **''AI Tools''** store. Do *not* use external knowledge or information from other vector stores (like AI News or AI Prompts).\n4.  **Be Honest:** If, after searching the designated **''AI Tools''** vector store, you cannot find information about a specific tool or feature, clearly state that the information is not available within your current AI Tools knowledge base. Do *not* invent answers or speculate.'\n);", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [2320, 40], "id": "990055bb-538a-4d00-9caa-3459b40cdb93", "name": "Create Table + Prompts1", "credentials": {"postgres": {"id": "IaFWN9RRYrNBDkZf", "name": "Postgres account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Insert chat type for AI Prompts\nINSERT INTO chat_types (user_id, name, type)\nVALUES (\n    '493cbf05-806c-4dd4-9ceb-4b58e38d3b9a',   -- UUID as text literal\n    'AI Prompts',\n    'AI Prompts'\n);\n\n-- Insert chat type for AI Tools\nINSERT INTO chat_types (user_id, name, type)\nVALUES (\n    '493cbf05-806c-4dd4-9ceb-4b58e38d3b9a',\n    'AI Tools',\n    'AI Tools'\n);\n\n-- Insert chat type for Personal Business\nINSERT INTO chat_types (user_id, name, type)\nVALUES (\n    '493cbf05-806c-4dd4-9ceb-4b58e38d3b9a',\n    'Personal Business',\n    'Personal Business'\n);\n\n-- Insert chat type for AI News\nINSERT INTO chat_types (user_id, name, type)\nVALUES (\n    '493cbf05-806c-4dd4-9ceb-4b58e38d3b9a',\n    'AI News',\n    'AI News'\n);\n", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.5, "position": [2080, 40], "id": "39ca1114-413d-47a7-bf8b-12ca239957d6", "name": "Chat Types", "credentials": {"postgres": {"id": "IaFWN9RRYrNBDkZf", "name": "Postgres account"}}}, {"parameters": {"promptType": "define", "text": "={{ $('Set Chat').item.json.chatInput }}", "options": {"systemMessage": "={{ $json.prompt }}\n\nTodays date and time: {{ $now }}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [1440, 40], "id": "5bf0995c-7dbe-434a-933a-76aadd1593cf", "name": "Cosmos"}, {"parameters": {"mode": "insert", "pineconeIndex": {"__rl": true, "value": "cosmos", "mode": "list", "cachedResultName": "cosmos"}, "options": {"pineconeNamespace": "ai_research"}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1, "position": [2440, 500], "id": "eac1b237-df87-4062-88d2-331a015cfb5c", "name": "Upload to AI Research", "credentials": {"pineconeApi": {"id": "CfhcNz156aFhl4QP", "name": "PineconeApi account"}}}], "pinData": {"YouTube": [{"json": {"headers": {"host": "kenkai.app.n8n.cloud", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "content-length": "119", "accept": "*/*", "accept-encoding": "gzip, br", "accept-language": "en-US,en;q=0.9", "cdn-loop": "cloudflare; loops=1; subreqs=1", "cf-connecting-ip": "2001:8003:f34a:2300:d563:91a3:ba0f:9ce3", "cf-ew-via": "15", "cf-ipcountry": "AU", "cf-ray": "9288386276c3d715-BNE", "cf-visitor": "{\"scheme\":\"https\"}", "cf-worker": "n8n.cloud", "content-type": "application/json", "dnt": "1", "origin": "https://thanos-kenkai.lovable.app", "priority": "u=1, i", "referer": "https://thanos-kenkai.lovable.app/", "sec-ch-ua": "\"Not:A-Brand\";v=\"24\", \"Chromium\";v=\"134\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"Windows\"", "sec-fetch-dest": "empty", "sec-fetch-mode": "cors", "sec-fetch-site": "cross-site", "x-forwarded-for": "2001:8003:f34a:2300:d563:91a3:ba0f:9ce3, ***************", "x-forwarded-host": "kenkai.app.n8n.cloud", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-forwarded-server": "traefik-prod-users-gwc-2-ddc688d69-smgqs", "x-is-trusted": "yes", "x-real-ip": "2001:8003:f34a:2300:d563:91a3:ba0f:9ce3"}, "params": {}, "query": {}, "body": {"youtubeUrl": "https://www.youtube.com/watch?v=HXrbsNsEtBI&ab_channel=AISearch", "timestamp": "2025-03-30T14:15:37.613Z"}, "webhookUrl": "https://kenkai.app.n8n.cloud/webhook/youtube", "executionMode": "production"}}], "News": [{"json": {"headers": {"host": "kenkai.app.n8n.cloud", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "content-length": "65", "accept": "*/*", "accept-encoding": "gzip, br", "accept-language": "en-US,en;q=0.9", "cdn-loop": "cloudflare; loops=1; subreqs=1", "cf-connecting-ip": "2001:8003:f34a:2300:d563:91a3:ba0f:9ce3", "cf-ew-via": "15", "cf-ipcountry": "AU", "cf-ray": "92883981452bd715-BNE", "cf-visitor": "{\"scheme\":\"https\"}", "cf-worker": "n8n.cloud", "content-type": "application/json", "dnt": "1", "origin": "https://thanos-kenkai.lovable.app", "priority": "u=1, i", "referer": "https://thanos-kenkai.lovable.app/", "sec-ch-ua": "\"Not:A-Brand\";v=\"24\", \"Chromium\";v=\"134\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"Windows\"", "sec-fetch-dest": "empty", "sec-fetch-mode": "cors", "sec-fetch-site": "cross-site", "x-forwarded-for": "2001:8003:f34a:2300:d563:91a3:ba0f:9ce3, ***************", "x-forwarded-host": "kenkai.app.n8n.cloud", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-forwarded-server": "traefik-prod-users-gwc-2-ddc688d69-smgqs", "x-is-trusted": "yes", "x-real-ip": "2001:8003:f34a:2300:d563:91a3:ba0f:9ce3"}, "params": {}, "query": {}, "body": {"newsQuery": "ai prompts", "timestamp": "2025-03-30T14:16:23.861Z"}, "webhookUrl": "https://kenkai.app.n8n.cloud/webhook/news", "executionMode": "production"}}], "Files": [{"json": {"headers": {"host": "kenkai.app.n8n.cloud", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "content-length": "449", "accept": "*/*", "accept-encoding": "gzip, br", "accept-language": "en-US,en;q=0.9", "cdn-loop": "cloudflare; loops=1; subreqs=1", "cf-connecting-ip": "2001:8003:f34a:2300:d563:91a3:ba0f:9ce3", "cf-ew-via": "15", "cf-ipcountry": "AU", "cf-ray": "928851ab90e7d710-BNE", "cf-visitor": "{\"scheme\":\"https\"}", "cf-worker": "n8n.cloud", "content-type": "application/json", "dnt": "1", "origin": "https://thanos-kenkai.lovable.app", "priority": "u=1, i", "referer": "https://thanos-kenkai.lovable.app/", "sec-ch-ua": "\"Not:A-Brand\";v=\"24\", \"Chromium\";v=\"134\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"Windows\"", "sec-fetch-dest": "empty", "sec-fetch-mode": "cors", "sec-fetch-site": "cross-site", "x-forwarded-for": "2001:8003:f34a:2300:d563:91a3:ba0f:9ce3, ***************", "x-forwarded-host": "kenkai.app.n8n.cloud", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-forwarded-server": "traefik-prod-users-gwc-2-ddc688d69-smgqs", "x-is-trusted": "yes", "x-real-ip": "2001:8003:f34a:2300:d563:91a3:ba0f:9ce3"}, "params": {}, "query": {}, "body": {"storagePath": "938eb0ab-0955-488e-be03-6b21e93a1eb5/8b179af2-6231-495b-af68-e1279be36c72-Decentralized_Teaching_Model_Pitch_FINAL_INCOMPLETE.pdf", "originalFilename": "Decentralized_Teaching_Model_Pitch_FINAL_INCOMPLETE.pdf", "downloadUrl": "https://boxqostddtptgzqctklx.supabase.co/storage/v1/object/public/file_uploads/938eb0ab-0955-488e-be03-6b21e93a1eb5/8b179af2-6231-495b-af68-e1279be36c72-Decentralized_Teaching_Model_Pitch_FINAL_INCOMPLETE.pdf"}, "webhookUrl": "https://kenkai.app.n8n.cloud/webhook/fileupload", "executionMode": "production"}}], "Webhook1": [{"json": {"headers": {"host": "kenkai.app.n8n.cloud", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "content-length": "20", "accept": "*/*", "accept-encoding": "gzip, br", "accept-language": "en-US,en;q=0.9", "cdn-loop": "cloudflare; loops=1; subreqs=1", "cf-connecting-ip": "2001:8003:f34a:2300:c847:d98d:8fd5:9b0a", "cf-ew-via": "15", "cf-ipcountry": "AU", "cf-ray": "928d61f3e7c7d70d-BNE", "cf-visitor": "{\"scheme\":\"https\"}", "cf-worker": "n8n.cloud", "content-type": "application/json", "dnt": "1", "origin": "https://ea490c25-7dd7-4d1f-bac2-98fdbd060bd4.lovableproject.com", "priority": "u=1, i", "referer": "https://ea490c25-7dd7-4d1f-bac2-98fdbd060bd4.lovableproject.com/", "sec-ch-ua": "\"Not:A-Brand\";v=\"24\", \"Chromium\";v=\"134\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"Windows\"", "sec-fetch-dest": "empty", "sec-fetch-mode": "cors", "sec-fetch-site": "cross-site", "x-forwarded-for": "2001:8003:f34a:2300:c847:d98d:8fd5:9b0a, ***************", "x-forwarded-host": "kenkai.app.n8n.cloud", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-forwarded-server": "traefik-prod-users-gwc-2-ddc688d69-smgqs", "x-is-trusted": "yes", "x-real-ip": "2001:8003:f34a:2300:c847:d98d:8fd5:9b0a"}, "params": {}, "query": {}, "body": {"query": "hi there"}, "webhookUrl": "https://kenkai.app.n8n.cloud/webhook/chat", "executionMode": "production"}}]}, "connections": {"Zep": {"ai_memory": [[{"node": "Cosmos", "type": "ai_memory", "index": 0}]]}, "Document Router": {"main": [[{"node": "Extract from PDF", "type": "main", "index": 0}], [{"node": "Extract from Text", "type": "main", "index": 0}], [{"node": "Extract from XLSX", "type": "main", "index": 0}], [{"node": "Extract from XLS", "type": "main", "index": 0}], [{"node": "Extract from CSV", "type": "main", "index": 0}], [{"node": "Extract from JSON", "type": "main", "index": 0}], [{"node": "Extract from HTML", "type": "main", "index": 0}]]}, "Extract from PDF": {"main": [[{"node": "Set Text", "type": "main", "index": 0}]]}, "Extract from CSV": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Extract from XLSX": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Extract from Text": {"main": [[{"node": "Set Text", "type": "main", "index": 0}]]}, "Extract from JSON": {"main": [[{"node": "Set Text", "type": "main", "index": 0}]]}, "Extract from HTML": {"main": [[{"node": "Set Text", "type": "main", "index": 0}]]}, "Text Classifier": {"main": [[{"node": "Upload to AI Research", "type": "main", "index": 0}], [{"node": "Upload to AI Prompts", "type": "main", "index": 0}], [{"node": "Upload to AI Tools", "type": "main", "index": 0}], [{"node": "Upload to Business", "type": "main", "index": 0}]]}, "4o": {"ai_languageModel": [[{"node": "Text Classifier", "type": "ai_languageModel", "index": 0}]]}, "4ox": {"ai_languageModel": [[{"node": "Cosmos", "type": "ai_languageModel", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Summarize", "type": "main", "index": 0}]]}, "Summarize": {"main": [[{"node": "Set Text", "type": "main", "index": 0}]]}, "Webhook1": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Download File": {"main": [[{"node": "Document Router", "type": "main", "index": 0}]]}, "Transcribe": {"main": [[{"node": "Set Text", "type": "main", "index": 0}]]}, "Set URL/Name": {"main": [[{"node": "Download File", "type": "main", "index": 0}]]}, "YouTube URL": {"main": [[{"node": "Transcribe", "type": "main", "index": 0}]]}, "Tavily": {"main": [[{"node": "Set Text", "type": "main", "index": 0}]]}, "Query": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "News": {"main": [[{"node": "Query", "type": "main", "index": 0}]]}, "YouTube": {"main": [[{"node": "YouTube URL", "type": "main", "index": 0}]]}, "Files": {"main": [[{"node": "Set URL/Name", "type": "main", "index": 0}]]}, "Set Chat": {"main": [[{"node": "Get Prompts", "type": "main", "index": 0}]]}, "Set Text": {"main": [[{"node": "Text Classifier", "type": "main", "index": 0}]]}, "Get Prompts": {"main": [[{"node": "Cosmos", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Upload to AI Research", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Upload to AI Research", "type": "ai_document", "index": 0}]]}, "Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Upload to AI Prompts", "type": "ai_embedding", "index": 0}]]}, "Embeddings OpenAI2": {"ai_embedding": [[{"node": "Upload to AI Tools", "type": "ai_embedding", "index": 0}]]}, "Embeddings OpenAI3": {"ai_embedding": [[{"node": "Upload to Business", "type": "ai_embedding", "index": 0}]]}, "Embeddings OpenAI4": {"ai_embedding": [[{"node": "RAG AI News", "type": "ai_embedding", "index": 0}]]}, "Embeddings OpenAI5": {"ai_embedding": [[{"node": "RAG AI Prompts", "type": "ai_embedding", "index": 0}]]}, "Embeddings OpenAI6": {"ai_embedding": [[{"node": "RAG AI Tools", "type": "ai_embedding", "index": 0}]]}, "Embeddings OpenAI7": {"ai_embedding": [[{"node": "RAG Personal Business", "type": "ai_embedding", "index": 0}]]}, "RAG AI News": {"ai_tool": [[{"node": "Cosmos", "type": "ai_tool", "index": 0}]]}, "RAG AI Prompts": {"ai_tool": [[{"node": "Cosmos", "type": "ai_tool", "index": 0}]]}, "RAG AI Tools": {"ai_tool": [[{"node": "Cosmos", "type": "ai_tool", "index": 0}]]}, "RAG Personal Business": {"ai_tool": [[{"node": "Cosmos", "type": "ai_tool", "index": 0}]]}, "Character Text Splitter1": {"ai_textSplitter": [[{"node": "Default Data Loader1", "type": "ai_textSplitter", "index": 0}]]}, "Default Data Loader1": {"ai_document": [[{"node": "Upload to AI Prompts", "type": "ai_document", "index": 0}]]}, "Character Text Splitter2": {"ai_textSplitter": [[{"node": "Default Data Loader2", "type": "ai_textSplitter", "index": 0}]]}, "Default Data Loader2": {"ai_document": [[{"node": "Upload to AI Tools", "type": "ai_document", "index": 0}]]}, "Character Text Splitter3": {"ai_textSplitter": [[{"node": "Default Data Loader3", "type": "ai_textSplitter", "index": 0}]]}, "Default Data Loader3": {"ai_document": [[{"node": "Upload to Business", "type": "ai_document", "index": 0}]]}, "Google Drive": {"main": [[{"node": "Document Router1", "type": "main", "index": 0}]]}, "Google Drive Trigger": {"main": [[{"node": "Set File Name/Mime", "type": "main", "index": 0}]]}, "File Updated": {"main": [[{"node": "Set File Name/Mime", "type": "main", "index": 0}]]}, "Set File Name/Mime": {"main": [[{"node": "Google Drive", "type": "main", "index": 0}]]}, "Document Router1": {"main": [[{"node": "Extract from PDF", "type": "main", "index": 0}], [{"node": "Extract from Text", "type": "main", "index": 0}], [{"node": "Extract from XLSX", "type": "main", "index": 0}], [{"node": "Extract from XLS", "type": "main", "index": 0}], [{"node": "Extract from CSV", "type": "main", "index": 0}], [{"node": "Extract from JSON", "type": "main", "index": 0}], [{"node": "Extract from HTML", "type": "main", "index": 0}]]}, "Extract from XLS": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Cosmos": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "0fa5d708-1c29-4c62-bcdb-c3f72dc74191", "meta": {"templateCredsSetupCompleted": true, "instanceId": "37d9fcb63a181bb3b92e99967fcf4e0183d85d74fb8651b374720bf02c8984e0"}, "id": "bvzmNGTjCvRxpZvK", "tags": []}